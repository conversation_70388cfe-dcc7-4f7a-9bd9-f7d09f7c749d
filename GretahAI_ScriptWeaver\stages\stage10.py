"""
Stage 10: Script Playground for GretahAI ScriptWeaver

This module provides an independent script experimentation and generation utility.
It provides functionality for:
- Loading optimized scripts as templates
- Selecting target test cases for generation
- Template-based AI script generation using proven patterns
- Managing generated scripts with proper metadata
- Independent access regardless of workflow stage

Key Features:
- **Always Accessible**: Can be accessed at any time, regardless of workflow stage
- **Template-Based Generation**: Uses optimized scripts as templates for new test cases
- **AI-Powered Adaptation**: Leverages Google AI to adapt templates to new requirements
- **Quality Preservation**: Maintains optimization patterns and best practices from templates
- **Playground Nature**: Functions as an experimental environment for script generation

The stage maintains the established architectural patterns:
- StateManager pattern with logging and st.rerun() after state changes
- Collapsible sections using st.expander() with expanded=True
- Manual progression with confirmation buttons
- Minimalist UI design with minimal instructional text
- Google AI integration via generate_llm_response

Functions:
    stage10_script_playground(state): Main Stage 10 function for script playground
"""

import os
import logging
import streamlit as st
import tempfile
import subprocess
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

# Import core dependencies
from state_manager import StateStage
from core.ai import generate_llm_response
from core.ai_helpers import clean_llm_response
from debug_utils import debug

# Import helper functions
from core.template_helpers import (
    get_optimized_scripts_for_templates,
    format_template_script_display,
    get_available_test_cases,
    format_test_case_display,
    validate_template_generation_inputs,
    create_template_generation_filename,
    extract_template_structure_info
)

from core.template_prompt_builder import (
    generate_template_based_script_prompt,
    enhance_template_prompt_with_context
)

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage10")


def stage10_script_playground(state):
    """
    Stage 10: Script Playground.

    This stage provides an independent, always-accessible script experimentation and generation utility.
    It loads optimized scripts as templates and generates new scripts for different test cases,
    preserving the optimization patterns and best practices from the templates.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>🎮 Script Playground</h2>", unsafe_allow_html=True)
    st.markdown("*Experiment with script generation using optimized scripts as templates*")

    debug("Stage 10: Script Playground accessed")

    # Initialize script storage if needed
    if not hasattr(state, '_script_storage') or state._script_storage is None:
        state._init_script_storage()

    # Load optimized scripts for templates
    optimized_scripts = get_optimized_scripts_for_templates(state._script_storage)
    available_test_cases = get_available_test_cases(state)

    # Check if we have the necessary data
    if not optimized_scripts:
        st.info("🎮 **Script Playground is Empty**")
        st.markdown("""
        **Welcome to the Script Playground!** This experimental environment allows you to generate new test scripts using optimized scripts as templates.

        **To get started:**
        - 📁 Complete the workflow through Stage 8 to create optimized scripts
        - ⚙️ Optimized scripts from Stage 8 will appear here as templates
        - 🎮 Use templates to experiment with script generation for different test cases

        **What you can do here:**
        - 🎯 Select optimized scripts as templates
        - 📋 Choose target test cases for generation
        - 🤖 Generate new scripts using AI with template patterns
        - 📥 Download and manage generated scripts
        """)

        # Provide navigation to create templates
        st.markdown("### 🚀 Create Templates")
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("📁 Start New Project", use_container_width=True, type="primary"):
                state.advance_to(StateStage.STAGE1_UPLOAD, "User started new project from Script Playground")
                st.rerun()
                return
        with col2:
            if st.button("⚙️ Generate Scripts", use_container_width=True):
                state.advance_to(StateStage.STAGE6_GENERATE, "User navigated to script generation from Script Playground")
                st.rerun()
                return
        with col3:
            if st.button("🔧 Optimize Scripts", use_container_width=True):
                state.advance_to(StateStage.STAGE8_OPTIMIZE, "User navigated to optimization from Script Playground")
                st.rerun()
                return
        return

    if not available_test_cases:
        st.warning("⚠️ **No Test Cases Available**")
        st.markdown("""
        You have optimized templates available, but no test cases to generate scripts for.
        Please upload a CSV file with test cases first.
        """)

        if st.button("📁 Upload Test Cases", use_container_width=True, type="primary"):
            state.advance_to(StateStage.STAGE1_UPLOAD, "User navigated to upload from Script Playground")
            st.rerun()
            return
        return

    # Template Selection Section
    with st.expander("🎯 Template Selection", expanded=True):
        st.markdown("**Select an optimized script to use as a template:**")

        # Create template options
        template_options = []
        template_map = {}

        for script in optimized_scripts:
            display_info = format_template_script_display(script)
            option_text = f"{display_info['title']} - {display_info['timestamp']}"
            template_options.append(option_text)
            template_map[option_text] = script

        if template_options:
            selected_template_option = st.selectbox(
                "Available Templates",
                template_options,
                key="template_selection"
            )

            selected_template = template_map[selected_template_option]

            # Display template details
            col1, col2 = st.columns(2)
            with col1:
                st.info(f"""
                **Template Details:**
                - Test Case: {selected_template.get('test_case_id', 'Unknown')}
                - Created: {format_template_script_display(selected_template)['timestamp']}
                - Size: {format_template_script_display(selected_template)['size_info']}
                """)

            with col2:
                st.info(f"""
                **Optimization Info:**
                - Status: ✅ Optimized
                - Type: {selected_template.get('type', 'Unknown').title()}
                - {format_template_script_display(selected_template)['optimization_info']}
                """)

            # Template preview toggle
            if st.checkbox("📄 Show Template Preview", key="show_template_preview"):
                st.markdown("**Template Code:**")
                template_content = selected_template.get('content', 'No content available')
                st.code(template_content, language='python')

    # Test Case Selection Section
    with st.expander("📋 Target Test Case Selection", expanded=True):
        st.markdown("**Select a test case to generate a script for:**")

        # Create test case options
        test_case_options = []
        test_case_map = {}

        for test_case in available_test_cases:
            option_text = format_test_case_display(test_case)
            test_case_options.append(option_text)
            test_case_map[option_text] = test_case

        if test_case_options:
            selected_test_case_option = st.selectbox(
                "Available Test Cases",
                test_case_options,
                key="test_case_selection"
            )

            selected_test_case = test_case_map[selected_test_case_option]

            # Display test case details
            tc_id = selected_test_case.get('Test Case ID', 'Unknown')
            tc_objective = selected_test_case.get('Test Case Objective', 'No objective specified')
            tc_steps = selected_test_case.get('Steps', [])

            st.info(f"""
            **Target Test Case:**
            - ID: {tc_id}
            - Objective: {tc_objective}
            - Steps: {len(tc_steps)} steps defined
            """)

    # Template-Based Generation Section
    with st.expander("🤖 Template-Based Generation", expanded=True):
        st.markdown("**Generate a new script using the selected template and test case:**")

        # Validate inputs
        if 'selected_template' in locals() and 'selected_test_case' in locals():
            is_valid, error_message = validate_template_generation_inputs(selected_template, selected_test_case)

            if not is_valid:
                st.error(f"❌ **Validation Error**: {error_message}")
                return

            # Generation controls
            col1, col2 = st.columns(2)

            with col1:
                custom_instructions = st.text_area(
                    "Custom Instructions (Optional)",
                    placeholder="Add any specific requirements or modifications...",
                    key="template_custom_instructions"
                )

            with col2:
                st.markdown("**Generation Settings:**")
                preserve_structure = st.checkbox("Preserve Template Structure", value=True, key="preserve_structure")
                include_error_handling = st.checkbox("Include Error Handling", value=True, key="include_error_handling")

            # Generate button
            if st.button("🚀 Generate Script from Template", use_container_width=True, type="primary"):
                _generate_script_from_template(
                    state, selected_template, selected_test_case,
                    custom_instructions, preserve_structure, include_error_handling
                )
        else:
            st.info("👆 Please select both a template and target test case above to enable generation.")

    # Script Execution Section (Independent from generation expander)
    _display_script_execution_section_if_available(state)

    # Script Playground Footer
    st.markdown("---")
    st.markdown("### 🎮 Script Playground Information")

    col1, col2 = st.columns(2)

    with col1:
        st.info("""
        **🔄 Always Accessible**: This Script Playground is available at any time, regardless of your current workflow stage.

        **🎯 Template-Based**: Experiment with script generation using proven optimization patterns from existing optimized scripts.
        """)

    with col2:
        st.info("""
        **🤖 AI-Powered**: Uses Google AI to adapt templates while preserving best practices and optimization patterns.

        **⚡ Independent**: Use this experimental environment without affecting your current workflow progress.
        """)

    # Optional workflow navigation (non-intrusive)
    with st.expander("🧭 Quick Workflow Navigation", expanded=False):
        st.markdown("*Optional: Jump to other workflow stages if needed*")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📁 Upload CSV (Stage 1)", use_container_width=True):
                debug("User navigated to Stage 1 from Script Playground")
                state.advance_to(StateStage.STAGE1_UPLOAD, "User navigated to Stage 1 from Script Playground")
                st.rerun()

        with col2:
            if st.button("🔧 Optimize Scripts (Stage 8)", use_container_width=True):
                debug("User navigated to Stage 8 from Script Playground")
                state.advance_to(StateStage.STAGE8_OPTIMIZE, "User navigated to Stage 8 from Script Playground")
                st.rerun()

        with col3:
            if st.button("📜 Script Browser (Stage 9)", use_container_width=True):
                debug("User navigated to Stage 9 from Script Playground")
                state.advance_to(StateStage.STAGE9_BROWSE, "User navigated to Stage 9 from Script Playground")
                st.rerun()



def _generate_script_from_template(state, template_script, target_test_case,
                                 custom_instructions, preserve_structure, include_error_handling):
    """
    Generate a new script using the selected template and test case.

    Args:
        state: StateManager instance
        template_script: Selected template script
        target_test_case: Target test case for generation
        custom_instructions: User's custom instructions
        preserve_structure: Whether to preserve template structure
        include_error_handling: Whether to include error handling
    """
    try:
        debug("Starting template-based script generation")

        with st.spinner("🤖 Generating script from template..."):
            # Extract template structure information
            template_structure_info = extract_template_structure_info(template_script.get('content', ''))

            # Generate the prompt
            base_prompt = generate_template_based_script_prompt(
                template_script=template_script,
                target_test_case=target_test_case,
                template_structure_info=template_structure_info,
                website_url=getattr(state, 'website_url', None)
            )

            # Enhance prompt with additional context
            additional_context = {
                'custom_instructions': custom_instructions if custom_instructions else None,
                'preserve_structure': preserve_structure,
                'include_error_handling': include_error_handling
            }

            enhanced_prompt = enhance_template_prompt_with_context(base_prompt, additional_context)

            # Generate script using Google AI
            debug("Calling Google AI for template-based script generation")
            generated_script = generate_llm_response(
                prompt=enhanced_prompt,
                model_name="gemini-2.0-flash",
                api_key=getattr(state, 'google_api_key', None),
                category="template_script_generation",
                context={
                    'template_test_case_id': template_script.get('test_case_id', 'unknown'),
                    'target_test_case_id': target_test_case.get('Test Case ID', 'unknown'),
                    'template_script_id': template_script.get('id', 'unknown'),
                    'generation_type': 'template_based'
                }
            )

            if generated_script and generated_script.strip():
                _handle_successful_generation(state, template_script, target_test_case, generated_script)
            else:
                st.error("❌ Failed to generate script. Please try again.")
                debug("Template-based script generation failed - empty response")

    except Exception as e:
        error_msg = f"Template-based script generation failed: {e}"
        st.error(f"❌ **Generation Error**: {error_msg}")
        debug(f"Template-based script generation error: {e}")


def _handle_successful_generation(state, template_script, target_test_case, generated_script):
    """
    Handle successful script generation with display, storage, and execution.

    Args:
        state: StateManager instance
        template_script: Template script used
        target_test_case: Target test case
        generated_script: Generated script content
    """
    try:
        debug("Handling successful template-based script generation")

        # Parse the generated script to extract clean Python code from markdown
        debug(f"Raw generated script length: {len(generated_script)} characters")
        parsed_script = clean_llm_response(generated_script, "python")
        debug(f"Parsed script length: {len(parsed_script)} characters")

        # Create filename
        filename = create_template_generation_filename(template_script, target_test_case)

        # Display success message
        st.success("✅ **Script Generated Successfully!**")

        # Display parsed script (clean Python code)
        st.markdown("### 📄 Generated Script")
        st.code(parsed_script, language='python')

        # Download and copy buttons
        col1, col2 = st.columns(2)
        with col1:
            st.download_button(
                label="📥 Download Script",
                data=parsed_script,
                file_name=filename,
                mime="text/x-python",
                use_container_width=True
            )

        with col2:
            if st.button("📋 Copy to Clipboard", use_container_width=True):
                st.code(parsed_script)  # This allows easy copying
                st.info("Script displayed above for copying")

        # Save to script storage
        template_metadata = {
            'generation_type': 'template_based',
            'template_script_id': template_script.get('id'),
            'template_test_case_id': template_script.get('test_case_id'),
            'target_test_case_id': target_test_case.get('Test Case ID'),
            'generation_timestamp': datetime.now().isoformat(),
            'template_based': True,
            'optimization_status': 'template_generated'
        }

        state.add_script_to_history(
            script_content=parsed_script,
            script_type='template_generated',
            step_no=None,
            file_path=filename,
            metadata=template_metadata
        )

        debug(f"Template-based script saved with filename: {filename}")

        # Display generation summary
        st.info(f"""
        **Generation Summary:**
        - Template: {template_script.get('test_case_id', 'Unknown')}
        - Target: {target_test_case.get('Test Case ID', 'Unknown')}
        - Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        - Filename: {filename}
        """)

        # Store generation data in session state for execution section (using parsed script)
        st.session_state['stage10_generated_script'] = {
            'script_content': parsed_script,
            'filename': filename,
            'target_test_case': target_test_case,
            'template_script': template_script,
            'generation_timestamp': datetime.now().isoformat(),
            'raw_generated_content': generated_script  # Keep original for debugging if needed
        }

        debug(f"Stored generated script data in session state for execution: {filename}")

    except Exception as e:
        error_msg = f"Failed to handle successful generation: {e}"
        st.error(f"❌ **Storage Error**: {error_msg}")
        debug(f"Error handling successful generation: {e}")


def _display_script_execution_section_if_available(state):
    """
    Display script execution section if a generated script is available in session state.

    This function checks for generated scripts in session state and displays the execution
    section independently from the generation expander to avoid Streamlit button conflicts.

    Args:
        state: StateManager instance
    """
    try:
        # Check if we have a generated script in session state
        if 'stage10_generated_script' not in st.session_state:
            return

        script_data = st.session_state['stage10_generated_script']
        generated_script = script_data.get('script_content')
        filename = script_data.get('filename')
        target_test_case = script_data.get('target_test_case')

        if not all([generated_script, filename, target_test_case]):
            debug("Incomplete script data in session state, skipping execution section")
            return

        debug(f"Displaying execution section for generated script: {filename}")

        # Script execution section (outside of any expander)
        st.markdown("---")  # Visual separator
        st.markdown("### 🧪 Test Generated Script")
        st.markdown("*Execute the generated script to verify it works correctly*")

        # Show script info
        st.info(f"""
        **Ready for Execution:**
        - Script: {filename}
        - Target Test Case: {target_test_case.get('Test Case ID', 'Unknown')}
        - Generated: {script_data.get('generation_timestamp', 'Unknown')}
        """)

        # Check if we have test results already stored in session state
        execution_key = f"stage10_execution_results_{filename}"
        has_test_results = execution_key in st.session_state

        if has_test_results:
            # Display existing test results
            test_results = st.session_state[execution_key]
            _display_execution_results(test_results, target_test_case)

        # Execution controls
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            st.markdown("**Execution Options:**")
            verbose_mode = st.checkbox(
                "Verbose Mode",
                value=False,
                key=f"stage10_verbose_{filename}",
                help="Show detailed execution output and logs"
            )

        with col2:
            # Execute button
            if st.button("🚀 Execute Script", use_container_width=True, type="primary", key=f"execute_{filename}"):
                _execute_generated_script(state, generated_script, filename, target_test_case, verbose_mode)

        with col3:
            # Clear script button
            if st.button("🗑️ Clear Script", use_container_width=True, key=f"clear_{filename}"):
                # Clear the generated script from session state
                if 'stage10_generated_script' in st.session_state:
                    del st.session_state['stage10_generated_script']
                # Clear execution results
                if execution_key in st.session_state:
                    del st.session_state[execution_key]
                st.success("✅ Script cleared successfully!")
                st.rerun()

    except Exception as e:
        error_msg = f"Failed to display script execution section: {e}"
        st.error(f"❌ **Execution Display Error**: {error_msg}")
        debug(f"Error displaying script execution section: {e}")


def _execute_generated_script(state, generated_script, filename, target_test_case, verbose_mode=False):
    """
    Execute the generated script using the same infrastructure as Stage 7/8.

    Args:
        state: StateManager instance
        generated_script: Generated script content
        filename: Script filename
        target_test_case: Target test case information
        verbose_mode: Whether to use verbose execution mode
    """
    try:
        debug(f"Executing generated script: {filename}")

        # Create a temporary file for the script
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_file:
            temp_file.write(generated_script)
            temp_script_path = temp_file.name

        debug(f"Created temporary script file: {temp_script_path}")

        with st.spinner(f"🧪 Executing generated script for {target_test_case.get('Test Case ID', 'Unknown')}..."):
            try:
                # Import the execution function from Stage 8
                from stages.stage8 import execute_test_script

                # Execute the script using the reusable function
                test_results = execute_test_script(
                    script_path=temp_script_path,
                    test_context=f"Stage 10 generated script ({filename})"
                )

                # Store results in session state for persistence
                execution_key = f"stage10_execution_results_{filename}"
                st.session_state[execution_key] = test_results

                debug(f"Script execution completed with success: {test_results.get('success', False)}")

                # Display immediate feedback
                if test_results.get('success', False):
                    st.success("✅ Generated script executed successfully!")
                else:
                    st.error("❌ Generated script execution failed. Check the results below.")

                # Display the detailed results
                _display_execution_results(test_results, target_test_case, verbose_mode)

            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_script_path)
                    debug(f"Cleaned up temporary script file: {temp_script_path}")
                except Exception as cleanup_error:
                    debug(f"Failed to clean up temporary file: {cleanup_error}")

    except Exception as e:
        error_msg = f"Script execution failed: {e}"
        st.error(f"❌ **Execution Error**: {error_msg}")
        debug(f"Error executing generated script: {e}")
        import traceback
        debug(f"Execution traceback: {traceback.format_exc()}")


def _display_execution_results(test_results, target_test_case, verbose_mode=False):
    """
    Display script execution results using the same patterns as Stage 7/8.

    Args:
        test_results: Test execution results dictionary
        target_test_case: Target test case information
        verbose_mode: Whether to show verbose details
    """
    try:
        debug("Displaying execution results in Stage 10")

        # Import the JUnit parser for result formatting
        from core.junit_parser import parse_junit_xml, format_test_results_for_display

        # Basic execution status
        success = test_results.get('success', False)
        returncode = test_results.get('returncode', -1)
        timestamp = test_results.get('timestamp', 'Unknown')

        # Display execution summary
        with st.expander("📋 Execution Results", expanded=True):
            col1, col2, col3 = st.columns(3)

            with col1:
                status_icon = "✅" if success else "❌"
                st.metric("Execution Status", f"{status_icon} {'Passed' if success else 'Failed'}")

            with col2:
                st.metric("Exit Code", returncode)

            with col3:
                st.metric("Timestamp", timestamp)

            # Show test case information
            tc_id = target_test_case.get('Test Case ID', 'Unknown')
            st.info(f"**Test Case:** {tc_id}")

            # Parse and display JUnit XML results if available
            xml_results = test_results.get('xml_results')
            if xml_results and "summary" in xml_results:
                st.markdown("### 📊 Test Execution Summary")

                summary = xml_results["summary"]
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("Total Tests", summary.get("total_tests", 0))
                with col2:
                    st.metric("Passed", summary.get("passed_tests", 0))
                with col3:
                    st.metric("Failed", summary.get("failed_tests", 0))
                with col4:
                    duration = summary.get("duration", 0)
                    st.metric("Duration", f"{duration:.2f}s")

            # Show performance metrics if available
            performance_metrics = test_results.get('performance_metrics', {})
            if performance_metrics:
                st.markdown("### ⚡ Performance Metrics")
                # Display key performance metrics
                for metric_name, metric_data in performance_metrics.get('aggregated', {}).items():
                    if isinstance(metric_data, dict) and 'average' in metric_data:
                        st.metric(
                            metric_name.replace('_', ' ').title(),
                            f"{metric_data['average']:.2f}",
                            help=f"Min: {metric_data.get('minimum', 'N/A')}, Max: {metric_data.get('maximum', 'N/A')}"
                        )

            # Show screenshots if any were captured
            screenshots = test_results.get('screenshots', [])
            if screenshots:
                st.markdown("### 📸 Screenshots Captured")
                for i, screenshot_path in enumerate(screenshots[:3]):  # Show up to 3 screenshots
                    if os.path.exists(screenshot_path):
                        st.image(screenshot_path, caption=f"Screenshot {i+1}: {os.path.basename(screenshot_path)}")

                if len(screenshots) > 3:
                    st.info(f"Showing first 3 screenshots. {len(screenshots) - 3} more available.")

            # Show artifacts if available
            artifacts = test_results.get('artifacts', {})
            if artifacts:
                st.markdown("### 📁 Test Artifacts")
                for artifact_name, artifact_path in artifacts.items():
                    if os.path.exists(artifact_path):
                        st.markdown(f"- **{artifact_name}**: `{artifact_path}`")

            # Show detailed output in verbose mode or on failure
            if verbose_mode or not success:
                st.markdown("### 📝 Execution Output")

                # Show stdout
                stdout = test_results.get('stdout', '')
                if stdout:
                    st.markdown("**Standard Output:**")
                    st.code(stdout, language="text")

                # Show stderr
                stderr = test_results.get('stderr', '')
                if stderr:
                    st.markdown("**Standard Error:**")
                    st.code(stderr, language="text")

                # Show failure details for failed tests
                if not success and stdout and ("FAILED" in stdout or "ERROR" in stdout):
                    st.markdown("**Failure Summary:**")
                    # Extract and show only failure lines
                    stdout_lines = stdout.split('\n')
                    failure_lines = [line for line in stdout_lines if
                                   "FAILED" in line or "ERROR" in line or
                                   "AssertionError" in line or "TimeoutException" in line]
                    if failure_lines:
                        st.code('\n'.join(failure_lines[:5]), language="text")  # Show first 5 failure lines
                        if len(failure_lines) > 5:
                            st.caption(f"Showing first 5 failure lines. {len(failure_lines) - 5} more available in verbose mode.")

            # Show success summary for passed tests
            elif success:
                st.success("🎉 **Script executed successfully!** The generated script works as expected.")

                # Show basic execution info
                if xml_results and "summary" in xml_results:
                    duration = xml_results["summary"].get("duration", 0)
                    tests_run = xml_results["summary"].get("total_tests", 0)
                    st.info(f"🎯 Executed {tests_run} test(s) in {duration:.2f} seconds")

        debug("Execution results displayed successfully")

    except Exception as e:
        error_msg = f"Failed to display execution results: {e}"
        st.error(f"❌ **Display Error**: {error_msg}")
        debug(f"Error displaying execution results: {e}")
